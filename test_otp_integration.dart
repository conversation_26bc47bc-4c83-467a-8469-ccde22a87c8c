// Simple test to verify OTP integration works
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get_it/get_it.dart';
import 'package:mcdc/core/di/app_injector.dart';
import 'package:mcdc/features/otp/domain/usecases/send_otp_usecase.dart';
import 'package:mcdc/features/otp/domain/usecases/verify_otp_usecase.dart';
import 'package:mcdc/features/otp/presentation/bloc/otp_cubit.dart';

void main() {
  group('OTP Integration Test', () {
    late GetIt sl;

    setUpAll(() async {
      // Initialize dependency injection
      sl = GetIt.instance;
      await AppInjector.init();
    });

    tearDownAll(() {
      sl.reset();
    });

    test('should create OTP cubit with dependencies', () {
      // Arrange
      final sendOtpUseCase = sl<SendOtpUseCase>();
      final verifyOtpUseCase = sl<VerifyOtpUseCase>();

      // Act
      final otpCubit = OtpCubit(
        sendOtpUseCase: sendOtpUseCase,
        verifyOtpUseCase: verifyOtpUseCase,
        email: '<EMAIL>',
        referenceCode: 'TEST123',
      );

      // Assert
      expect(otpCubit, isNotNull);
      expect(otpCubit.state.email, equals('<EMAIL>'));
      expect(otpCubit.state.referenceCode, equals('TEST123'));

      // Cleanup
      otpCubit.close();
    });

    test('should have all dependencies registered', () {
      // Assert
      expect(sl.isRegistered<SendOtpUseCase>(), isTrue);
      expect(sl.isRegistered<VerifyOtpUseCase>(), isTrue);
    });
  });
}
