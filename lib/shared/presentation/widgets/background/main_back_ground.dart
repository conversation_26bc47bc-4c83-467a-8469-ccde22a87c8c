import 'package:flutter/material.dart';
import 'package:mcdc/core/constants/app_colors.dart';

class MainBackground extends StatelessWidget {
  final Widget? child;
  final EdgeInsetsGeometry? padding;

  const MainBackground({super.key, this.child, this.padding});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: padding ?? EdgeInsets.zero,
      decoration: const BoxDecoration(color: AppColors.surfaceDefault),
      child: child,
    );
  }
}
