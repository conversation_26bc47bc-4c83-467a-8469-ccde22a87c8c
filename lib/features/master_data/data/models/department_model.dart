import 'package:freezed_annotation/freezed_annotation.dart';
import '../../../user/domain/entities/organization.dart';

part 'department_model.freezed.dart';
part 'department_model.g.dart';

@freezed
abstract class DepartmentModel with _$DepartmentModel {
  const factory DepartmentModel({
    required int id,
    required int appMasMinistryId,
    required String nameTh,
    required String nameEn,
  }) = _DepartmentModel;

  factory DepartmentModel.fromJson(Map<String, dynamic> json) =>
      _$DepartmentModelFromJson(json);

  // Custom fromJson to handle API field names
  factory DepartmentModel.fromApiJson(Map<String, dynamic> json) {
    return DepartmentModel(
      id: json['id'] as int,
      appMasMinistryId: json['app_mas_ministry_id'] as int,
      nameTh: json['name_th'] as String,
      nameEn: json['name_en'] as String,
    );
  }
}

extension DepartmentModelExtension on DepartmentModel {
  /// Convert DepartmentModel to Organization entity
  Organization toEntity() {
    return Organization(
      id: id,
      name: 'department_$id', // Generate a unique name
      displayName: nameTh, // Use Thai name as display name
      parentId: appMasMinistryId,
    );
  }
}
