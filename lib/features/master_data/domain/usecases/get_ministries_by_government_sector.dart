import 'package:dartz/dartz.dart';
import 'package:mcdc/core/error/failures.dart';
import 'package:mcdc/core/usecases/usecase.dart';
import '../../../user/domain/entities/organization.dart';
import '../repositories/master_data_repository.dart';

class GetMinistriesByGovernmentSector implements UseCase<List<Organization>, int> {
  final MasterDataRepository repository;

  GetMinistriesByGovernmentSector(this.repository);

  @override
  Future<Either<Failure, List<Organization>>> call(int governmentSectorId) async {
    return await repository.getMinistriesByGovernmentSector(governmentSectorId);
  }
}
