import 'package:dartz/dartz.dart';
import 'package:mcdc/core/error/failures.dart';
import 'package:mcdc/core/usecases/usecase.dart';
import '../../../user/domain/entities/organization.dart';
import '../repositories/master_data_repository.dart';

class GetDepartmentsByMinistry implements UseCase<List<Organization>, int> {
  final MasterDataRepository repository;

  GetDepartmentsByMinistry(this.repository);

  @override
  Future<Either<Failure, List<Organization>>> call(int ministryId) async {
    return await repository.getDepartmentsByMinistry(ministryId);
  }
}
