import 'package:dartz/dartz.dart';
import 'package:mcdc/core/error/failures.dart';
import 'package:mcdc/core/usecases/usecase.dart';
import '../entities/government_sector.dart';
import '../repositories/master_data_repository.dart';

class GetGovernmentSectors implements UseCase<List<GovernmentSector>, NoParams> {
  final MasterDataRepository repository;

  GetGovernmentSectors(this.repository);

  @override
  Future<Either<Failure, List<GovernmentSector>>> call(NoParams params) async {
    return await repository.getGovernmentSectors();
  }
}
