import 'package:dartz/dartz.dart';
import 'package:mcdc/core/error/failures.dart';
import '../entities/member_type.dart';
import '../entities/government_sector.dart';
import '../../../user/domain/entities/organization.dart';

abstract class MasterDataRepository {
  Future<Either<Failure, List<MemberType>>> getMemberTypes();
  Future<Either<Failure, List<GovernmentSector>>> getGovernmentSectors();
  Future<Either<Failure, List<Organization>>> getMinistriesByGovernmentSector(
    int governmentSectorId,
  );
  Future<Either<Failure, List<Organization>>> getDepartmentsByMinistry(
    int ministryId,
  );
}
