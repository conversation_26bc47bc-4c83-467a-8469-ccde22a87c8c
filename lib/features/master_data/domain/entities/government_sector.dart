import 'package:equatable/equatable.dart';

/// Government sector entity for government agency selection
class GovernmentSector extends Equatable {
  final int id;
  final String nameTh;
  final String nameEn;

  const GovernmentSector({
    required this.id,
    required this.nameTh,
    required this.nameEn,
  });

  @override
  List<Object?> get props => [id, nameTh, nameEn];

  GovernmentSector copyWith({
    int? id,
    String? nameTh,
    String? nameEn,
  }) {
    return GovernmentSector(
      id: id ?? this.id,
      nameTh: nameTh ?? this.nameTh,
      nameEn: nameEn ?? this.nameEn,
    );
  }

  /// Get display name based on current locale
  /// For now, we'll use Thai as default
  String get displayName => nameTh;
}
