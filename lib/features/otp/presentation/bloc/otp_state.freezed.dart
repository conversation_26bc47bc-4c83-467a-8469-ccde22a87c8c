// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'otp_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
/// @nodoc
mixin _$OtpState {

 String get otpCode; int get remainingSeconds; bool get isLoading; bool get canResend; String? get errorMessage; String get email; String get referenceCode; OtpStatus get status;
/// Create a copy of OtpState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$OtpStateCopyWith<OtpState> get copyWith => _$OtpStateCopyWithImpl<OtpState>(this as OtpState, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is OtpState&&(identical(other.otpCode, otpCode) || other.otpCode == otpCode)&&(identical(other.remainingSeconds, remainingSeconds) || other.remainingSeconds == remainingSeconds)&&(identical(other.isLoading, isLoading) || other.isLoading == isLoading)&&(identical(other.canResend, canResend) || other.canResend == canResend)&&(identical(other.errorMessage, errorMessage) || other.errorMessage == errorMessage)&&(identical(other.email, email) || other.email == email)&&(identical(other.referenceCode, referenceCode) || other.referenceCode == referenceCode)&&(identical(other.status, status) || other.status == status));
}


@override
int get hashCode => Object.hash(runtimeType,otpCode,remainingSeconds,isLoading,canResend,errorMessage,email,referenceCode,status);

@override
String toString() {
  return 'OtpState(otpCode: $otpCode, remainingSeconds: $remainingSeconds, isLoading: $isLoading, canResend: $canResend, errorMessage: $errorMessage, email: $email, referenceCode: $referenceCode, status: $status)';
}


}

/// @nodoc
abstract mixin class $OtpStateCopyWith<$Res>  {
  factory $OtpStateCopyWith(OtpState value, $Res Function(OtpState) _then) = _$OtpStateCopyWithImpl;
@useResult
$Res call({
 String otpCode, int remainingSeconds, bool isLoading, bool canResend, String? errorMessage, String email, String referenceCode, OtpStatus status
});




}
/// @nodoc
class _$OtpStateCopyWithImpl<$Res>
    implements $OtpStateCopyWith<$Res> {
  _$OtpStateCopyWithImpl(this._self, this._then);

  final OtpState _self;
  final $Res Function(OtpState) _then;

/// Create a copy of OtpState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? otpCode = null,Object? remainingSeconds = null,Object? isLoading = null,Object? canResend = null,Object? errorMessage = freezed,Object? email = null,Object? referenceCode = null,Object? status = null,}) {
  return _then(_self.copyWith(
otpCode: null == otpCode ? _self.otpCode : otpCode // ignore: cast_nullable_to_non_nullable
as String,remainingSeconds: null == remainingSeconds ? _self.remainingSeconds : remainingSeconds // ignore: cast_nullable_to_non_nullable
as int,isLoading: null == isLoading ? _self.isLoading : isLoading // ignore: cast_nullable_to_non_nullable
as bool,canResend: null == canResend ? _self.canResend : canResend // ignore: cast_nullable_to_non_nullable
as bool,errorMessage: freezed == errorMessage ? _self.errorMessage : errorMessage // ignore: cast_nullable_to_non_nullable
as String?,email: null == email ? _self.email : email // ignore: cast_nullable_to_non_nullable
as String,referenceCode: null == referenceCode ? _self.referenceCode : referenceCode // ignore: cast_nullable_to_non_nullable
as String,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as OtpStatus,
  ));
}

}


/// @nodoc


class _OtpState implements OtpState {
  const _OtpState({this.otpCode = '', this.remainingSeconds = 300, this.isLoading = false, this.canResend = false, this.errorMessage, this.email = '', this.referenceCode = '', this.status = OtpStatus.initial});
  

@override@JsonKey() final  String otpCode;
@override@JsonKey() final  int remainingSeconds;
@override@JsonKey() final  bool isLoading;
@override@JsonKey() final  bool canResend;
@override final  String? errorMessage;
@override@JsonKey() final  String email;
@override@JsonKey() final  String referenceCode;
@override@JsonKey() final  OtpStatus status;

/// Create a copy of OtpState
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$OtpStateCopyWith<_OtpState> get copyWith => __$OtpStateCopyWithImpl<_OtpState>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _OtpState&&(identical(other.otpCode, otpCode) || other.otpCode == otpCode)&&(identical(other.remainingSeconds, remainingSeconds) || other.remainingSeconds == remainingSeconds)&&(identical(other.isLoading, isLoading) || other.isLoading == isLoading)&&(identical(other.canResend, canResend) || other.canResend == canResend)&&(identical(other.errorMessage, errorMessage) || other.errorMessage == errorMessage)&&(identical(other.email, email) || other.email == email)&&(identical(other.referenceCode, referenceCode) || other.referenceCode == referenceCode)&&(identical(other.status, status) || other.status == status));
}


@override
int get hashCode => Object.hash(runtimeType,otpCode,remainingSeconds,isLoading,canResend,errorMessage,email,referenceCode,status);

@override
String toString() {
  return 'OtpState(otpCode: $otpCode, remainingSeconds: $remainingSeconds, isLoading: $isLoading, canResend: $canResend, errorMessage: $errorMessage, email: $email, referenceCode: $referenceCode, status: $status)';
}


}

/// @nodoc
abstract mixin class _$OtpStateCopyWith<$Res> implements $OtpStateCopyWith<$Res> {
  factory _$OtpStateCopyWith(_OtpState value, $Res Function(_OtpState) _then) = __$OtpStateCopyWithImpl;
@override @useResult
$Res call({
 String otpCode, int remainingSeconds, bool isLoading, bool canResend, String? errorMessage, String email, String referenceCode, OtpStatus status
});




}
/// @nodoc
class __$OtpStateCopyWithImpl<$Res>
    implements _$OtpStateCopyWith<$Res> {
  __$OtpStateCopyWithImpl(this._self, this._then);

  final _OtpState _self;
  final $Res Function(_OtpState) _then;

/// Create a copy of OtpState
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? otpCode = null,Object? remainingSeconds = null,Object? isLoading = null,Object? canResend = null,Object? errorMessage = freezed,Object? email = null,Object? referenceCode = null,Object? status = null,}) {
  return _then(_OtpState(
otpCode: null == otpCode ? _self.otpCode : otpCode // ignore: cast_nullable_to_non_nullable
as String,remainingSeconds: null == remainingSeconds ? _self.remainingSeconds : remainingSeconds // ignore: cast_nullable_to_non_nullable
as int,isLoading: null == isLoading ? _self.isLoading : isLoading // ignore: cast_nullable_to_non_nullable
as bool,canResend: null == canResend ? _self.canResend : canResend // ignore: cast_nullable_to_non_nullable
as bool,errorMessage: freezed == errorMessage ? _self.errorMessage : errorMessage // ignore: cast_nullable_to_non_nullable
as String?,email: null == email ? _self.email : email // ignore: cast_nullable_to_non_nullable
as String,referenceCode: null == referenceCode ? _self.referenceCode : referenceCode // ignore: cast_nullable_to_non_nullable
as String,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as OtpStatus,
  ));
}


}

// dart format on
