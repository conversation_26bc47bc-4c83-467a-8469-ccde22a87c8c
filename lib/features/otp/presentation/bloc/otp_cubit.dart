import 'dart:async';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import '../../domain/usecases/send_otp_usecase.dart';
import '../../domain/usecases/verify_otp_usecase.dart';
import 'otp_state.dart';

class OtpCubit extends Cubit<OtpState> {
  final SendOtpUseCase _sendOtpUseCase;
  final VerifyOtpUseCase _verifyOtpUseCase;
  Timer? _timer;
  String? _currentToken;

  OtpCubit({
    required SendOtpUseCase sendOtpUseCase,
    required VerifyOtpUseCase verifyOtpUseCase,
    String email = '',
    String referenceCode = '',
  }) : _sendOtpUseCase = sendOtpUseCase,
       _verifyOtpUseCase = verifyOtpUseCase,
       super(
         OtpState(
           email: email,
           referenceCode: referenceCode,
           status: OtpStatus.initial,
         ),
       ) {
    if (email.isNotEmpty) {
      sendOtp(email);
    }
  }

  void _startTimer() {
    _timer?.cancel();
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (state.remainingSeconds > 0) {
        emit(
          state.copyWith(
            remainingSeconds: state.remainingSeconds - 1,
            canResend: state.remainingSeconds <= 1,
          ),
        );
      } else {
        timer.cancel();
        emit(state.copyWith(canResend: true));
      }
    });
  }

  void updateOtpCode(String code) {
    emit(state.copyWith(otpCode: code, errorMessage: null));
  }

  Future<void> sendOtp(String email) async {
    emit(
      state.copyWith(status: OtpStatus.loading, isLoading: true, email: email),
    );

    final result = await _sendOtpUseCase(SendOtpParams(email: email));

    result.fold(
      (failure) {
        emit(
          state.copyWith(
            status: OtpStatus.error,
            isLoading: false,
            errorMessage: failure.message,
          ),
        );
      },
      (response) {
        _currentToken = response.token;
        emit(
          state.copyWith(
            status: OtpStatus.initial,
            isLoading: false,
            email: email,
            referenceCode: response.refCode,
            remainingSeconds: 300,
            canResend: false,
          ),
        );
        _startTimer();
      },
    );
  }

  Future<void> verifyOtp(AppLocalizations l10n) async {
    if (state.otpCode.length != 6) {
      emit(
        state.copyWith(
          status: OtpStatus.error,
          errorMessage: l10n.otpInvalidCode,
        ),
      );
      return;
    }

    if (_currentToken == null) {
      emit(
        state.copyWith(
          status: OtpStatus.error,
          errorMessage: 'No OTP token available. Please request a new OTP.',
        ),
      );
      return;
    }

    emit(state.copyWith(status: OtpStatus.loading, isLoading: true));

    final result = await _verifyOtpUseCase(
      VerifyOtpParams(
        token: _currentToken!,
        otp: state.otpCode,
        refCode: state.referenceCode,
      ),
    );

    result.fold(
      (failure) {
        emit(
          state.copyWith(
            status: OtpStatus.error,
            isLoading: false,
            errorMessage: failure.message,
          ),
        );
      },
      (response) {
        _currentToken = response.token; // Update token for future use
        emit(state.copyWith(status: OtpStatus.success, isLoading: false));
      },
    );
  }

  Future<void> resendOtp(AppLocalizations l10n) async {
    if (!state.canResend) return;

    emit(
      state.copyWith(
        otpCode: '',
        isLoading: true,
        canResend: false,
        status: OtpStatus.loading,
      ),
    );

    final result = await _sendOtpUseCase(SendOtpParams(email: state.email));

    result.fold(
      (failure) {
        emit(
          state.copyWith(
            isLoading: false,
            status: OtpStatus.error,
            errorMessage: failure.message,
          ),
        );
      },
      (response) {
        _currentToken = response.token;
        emit(
          state.copyWith(
            otpCode: '',
            remainingSeconds: 300,
            canResend: false,
            isLoading: false,
            status: OtpStatus.resendSuccess,
            referenceCode: response.refCode,
          ),
        );
        _startTimer();
      },
    );
  }

  String? getErrorMessage(AppLocalizations l10n) {
    return state.errorMessage;
  }

  @override
  Future<void> close() {
    _timer?.cancel();
    return super.close();
  }
}
