import 'package:equatable/equatable.dart';
import 'package:mcdc/core/enums/user_type.dart';

/// User entity representing a member user
class User extends Equatable {
  final int id;
  final String name;
  final String firstName;
  final String lastName;
  final String email;
  final String phone;
  final String? identityCardNo;
  final String username;
  final int appMasMemberTypeId;
  final String appMasMemberTypeDisplay;
  final int? appMasGovernmentSectorId;
  final String appMasGovernmentSectorDisplay;
  final int? appMasMinistryId;
  final String appMasMinistryDisplay;
  final int? appMasDepartmentId;
  final String appMasDepartmentDisplay;
  final String? website;
  final DateTime createDate;
  final String isNotification;
  final String status;
  final String statusDisplay;
  final String lang;
  final UserType userType;

  const User({
    required this.id,
    required this.name,
    required this.firstName,
    required this.lastName,
    required this.email,
    required this.phone,
    this.identityCardNo,
    required this.username,
    required this.appMasMemberTypeId,
    required this.appMasMemberTypeDisplay,
    this.appMasGovernmentSectorId,
    required this.appMasGovernmentSectorDisplay,
    this.appMasMinistryId,
    required this.appMasMinistryDisplay,
    this.appMasDepartmentId,
    required this.appMasDepartmentDisplay,
    this.website,
    required this.createDate,
    required this.isNotification,
    required this.status,
    required this.statusDisplay,
    required this.lang,
    required this.userType,
  });

  @override
  List<Object?> get props => [
    id,
    name,
    firstName,
    lastName,
    email,
    phone,
    identityCardNo,
    username,
    appMasMemberTypeId,
    appMasMemberTypeDisplay,
    appMasGovernmentSectorId,
    appMasGovernmentSectorDisplay,
    appMasMinistryId,
    appMasMinistryDisplay,
    appMasDepartmentId,
    appMasDepartmentDisplay,
    website,
    createDate,
    isNotification,
    status,
    statusDisplay,
    lang,
    userType,
  ];

  User copyWith({
    int? id,
    String? name,
    String? firstName,
    String? lastName,
    String? email,
    String? phone,
    String? identityCardNo,
    String? username,
    int? appMasMemberTypeId,
    String? appMasMemberTypeDisplay,
    int? appMasGovernmentSectorId,
    String? appMasGovernmentSectorDisplay,
    int? appMasMinistryId,
    String? appMasMinistryDisplay,
    int? appMasDepartmentId,
    String? appMasDepartmentDisplay,
    String? website,
    DateTime? createDate,
    String? isNotification,
    String? status,
    String? statusDisplay,
    String? lang,
    UserType? userType,
  }) {
    return User(
      id: id ?? this.id,
      name: name ?? this.name,
      firstName: firstName ?? this.firstName,
      lastName: lastName ?? this.lastName,
      email: email ?? this.email,
      phone: phone ?? this.phone,
      identityCardNo: identityCardNo ?? this.identityCardNo,
      username: username ?? this.username,
      appMasMemberTypeId: appMasMemberTypeId ?? this.appMasMemberTypeId,
      appMasMemberTypeDisplay:
          appMasMemberTypeDisplay ?? this.appMasMemberTypeDisplay,
      appMasGovernmentSectorId:
          appMasGovernmentSectorId ?? this.appMasGovernmentSectorId,
      appMasGovernmentSectorDisplay:
          appMasGovernmentSectorDisplay ?? this.appMasGovernmentSectorDisplay,
      appMasMinistryId: appMasMinistryId ?? this.appMasMinistryId,
      appMasMinistryDisplay:
          appMasMinistryDisplay ?? this.appMasMinistryDisplay,
      appMasDepartmentId: appMasDepartmentId ?? this.appMasDepartmentId,
      appMasDepartmentDisplay:
          appMasDepartmentDisplay ?? this.appMasDepartmentDisplay,
      website: website ?? this.website,
      createDate: createDate ?? this.createDate,
      isNotification: isNotification ?? this.isNotification,
      status: status ?? this.status,
      statusDisplay: statusDisplay ?? this.statusDisplay,
      lang: lang ?? this.lang,
      userType: userType ?? this.userType,
    );
  }
}
