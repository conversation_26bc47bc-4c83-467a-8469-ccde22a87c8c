import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:mcdc/core/constants/app_colors.dart';
import 'package:mcdc/shared/presentation/widgets/background/main_back_ground.dart';
import 'package:mcdc/shared/presentation/widgets/button/primary_button.dart';
import 'package:url_launcher/url_launcher.dart';

@RoutePage()
class HowToLoginThaiIdPage extends StatelessWidget {
  const HowToLoginThaiIdPage({super.key});

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;

    return Scaffold(
      body: SafeArea(
        child: MainBackground(
          child: Column(
            children: [
              _buildAppBar(context, l10n),
              _buildIllustration(),
              _buildContent(l10n),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAppBar(BuildContext context, AppLocalizations l10n) {
    return Container(
      width: double.infinity,
      color: Colors.white,
      child: Column(
        children: [
          // AppBar
          Container(
            padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
            child: Row(
              children: [
                GestureDetector(
                  onTap: () => context.router.pop(),
                  child: SvgPicture.asset(
                    'assets/icons/arrow_left.svg',
                    width: 24.w,
                    height: 24.h,
                    colorFilter: const ColorFilter.mode(
                      AppColors.textDefaultDark,
                      BlendMode.srcIn,
                    ),
                  ),
                ),
                SizedBox(width: 16.w),
                Text(
                  l10n.howToLoginThaIDPageTitle,
                  style: TextStyle(
                    fontSize: 20.sp,
                    fontWeight: FontWeight.w600,
                    color: AppColors.textDefaultDark,
                    height: 1.5,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildIllustration() {
    return Container(
      width: 132.w,
      height: 130.h,
      margin: EdgeInsets.only(top: 24.h),
      child: Image.asset(
        'assets/images/how_to_thai_id.png',
        fit: BoxFit.contain,
      ),
    );
  }

  Widget _buildContent(AppLocalizations l10n) {
    return Expanded(
      child: SingleChildScrollView(
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: 24.w, vertical: 24.h),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // ThaID Title
              Text(
                l10n.thaIDTitle,
                style: TextStyle(
                  fontSize: 20.sp,
                  fontWeight: FontWeight.w600,
                  color: AppColors.textDefaultDark,
                  height: 1.5,
                ),
              ),
              SizedBox(height: 16.h),

              // Description
              Text(
                l10n.thaIDDescription,
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w400,
                  color: AppColors.textDefaultDark,
                  height: 1.5,
                ),
              ),
              SizedBox(height: 16.h),

              // Registration Method Title
              Text(
                l10n.registrationMethodTitle,
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w600,
                  color: AppColors.textDefaultDark,
                  height: 1.5,
                ),
              ),
              SizedBox(height: 16.h),

              // Registration Steps
              Text(
                l10n.registrationSteps,
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w400,
                  color: AppColors.textDefaultDark,
                  height: 1.5,
                ),
              ),
              SizedBox(height: 16.h),

              // Self Registration Method
              Container(
                width: double.infinity,
                padding: EdgeInsets.all(16.w),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(8.r),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.05),
                      blurRadius: 4,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      l10n.selfRegistrationTitle,
                      style: TextStyle(
                        fontSize: 16.sp,
                        fontWeight: FontWeight.w600,
                        color: AppColors.textDefaultDark,
                        height: 1.5,
                      ),
                    ),
                    SizedBox(height: 8.h),
                    Text(
                      l10n.selfRegistrationDescription,
                      style: TextStyle(
                        fontSize: 16.sp,
                        fontWeight: FontWeight.w400,
                        color: AppColors.textDefaultDark,
                        height: 1.5,
                      ),
                    ),
                  ],
                ),
              ),
              SizedBox(height: 16.h),

              // Staff Registration Method
              Container(
                width: double.infinity,
                padding: EdgeInsets.all(16.w),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(8.r),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.05),
                      blurRadius: 4,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      l10n.staffRegistrationTitle,
                      style: TextStyle(
                        fontSize: 16.sp,
                        fontWeight: FontWeight.w600,
                        color: AppColors.textDefaultDark,
                        height: 1.5,
                      ),
                    ),
                    SizedBox(height: 8.h),
                    Text(
                      l10n.staffRegistrationDescription,
                      style: TextStyle(
                        fontSize: 16.sp,
                        fontWeight: FontWeight.w400,
                        color: AppColors.textDefaultDark,
                        height: 1.5,
                      ),
                    ),
                  ],
                ),
              ),
              SizedBox(height: 24.h),

              // Download Button
              PrimaryButton(
                text: l10n.downloadThaID,
                onPressed: _launchThaiDApp,
                width: double.infinity,
                height: 52.h,
                borderRadius: 20.r,
              ),
              SizedBox(height: 24.h),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _launchThaiDApp() async {
    // Thai ID app links
    const String androidPackageName = 'th.go.dopa.bora.digital_id';
    const String iosAppId = '1553212280';

    final Uri androidUri = Uri.parse('market://details?id=$androidPackageName');
    final Uri iosUri = Uri.parse('https://apps.apple.com/app/id$iosAppId');
    final Uri fallbackUri = Uri.parse(
      'https://apps.apple.com/th/app/thaid/id$iosAppId',
    );

    try {
      // Try to launch the appropriate store
      if (await canLaunchUrl(androidUri)) {
        await launchUrl(androidUri);
      } else if (await canLaunchUrl(iosUri)) {
        await launchUrl(iosUri);
      } else {
        await launchUrl(fallbackUri);
      }
    } catch (e) {
      // Fallback to web version
      await launchUrl(fallbackUri);
    }
  }
}
