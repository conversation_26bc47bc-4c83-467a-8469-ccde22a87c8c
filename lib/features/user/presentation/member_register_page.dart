import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mcdc/core/constants/app_colors.dart';
import 'package:mcdc/core/constants/app_fonts.dart';
import 'package:mcdc/core/di/app_injector.dart';
import 'package:mcdc/core/routes/router.gr.dart';
import 'package:mcdc/core/utils/validators.dart';
import 'package:mcdc/features/user/presentation/bloc/check_id_card_cubit.dart';
import 'package:mcdc/features/user/presentation/bloc/check_id_card_state.dart';
import 'package:mcdc/features/user/presentation/bloc/validate_register_cubit.dart';
import 'package:mcdc/features/user/presentation/bloc/validate_register_state.dart';
import 'package:mcdc/features/user/presentation/bloc/additional_data_cubit.dart';
import 'package:mcdc/features/user/presentation/bloc/additional_data_state.dart';
import 'package:mcdc/features/master_data/domain/entities/member_type.dart';
import 'package:mcdc/features/master_data/domain/entities/government_sector.dart';
import 'package:mcdc/features/user/domain/entities/organization.dart';
import 'package:mcdc/shared/presentation/widgets/appbar/app_bar_common.dart';
import 'package:mcdc/shared/presentation/widgets/button/primary_button.dart';
import 'package:mcdc/shared/presentation/widgets/input/custom_text_field.dart';
import 'package:mcdc/shared/presentation/widgets/input/custom_dropdown_field.dart';

@RoutePage()
class MemberRegisterPage extends StatefulWidget {
  const MemberRegisterPage({super.key});

  @override
  State<MemberRegisterPage> createState() => _MemberRegisterPageState();
}

class _MemberRegisterPageState extends State<MemberRegisterPage> {
  // Step tracking
  int _currentStep = 1;

  // Controllers for first step
  final TextEditingController _idCardController = TextEditingController();

  // Controllers for second step
  final TextEditingController _usernameController = TextEditingController();
  final TextEditingController _emailController = TextEditingController();
  final TextEditingController _phoneController = TextEditingController();
  final TextEditingController _passwordController = TextEditingController();
  final TextEditingController _confirmPasswordController =
      TextEditingController();

  // Controllers for third step
  final TextEditingController _firstNameController = TextEditingController();
  final TextEditingController _lastNameController = TextEditingController();
  final TextEditingController _departmentNameController =
      TextEditingController();

  final _formKey = GlobalKey<FormState>();
  late CheckIdCardCubit _checkIdCardCubit;
  late ValidateRegisterCubit _validateRegisterCubit;
  late AdditionalDataCubit _additionalDataCubit;

  @override
  void initState() {
    super.initState();
    _checkIdCardCubit = AppInjector.get<CheckIdCardCubit>();
    _validateRegisterCubit = AppInjector.get<ValidateRegisterCubit>();
    _additionalDataCubit = AppInjector.get<AdditionalDataCubit>();

    // Check if returning from OTP verification
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _checkIfReturningFromOtp();
    });
  }

  @override
  void dispose() {
    _idCardController.dispose();
    _usernameController.dispose();
    _emailController.dispose();
    _phoneController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    _firstNameController.dispose();
    _lastNameController.dispose();
    _departmentNameController.dispose();
    _checkIdCardCubit.close();
    _validateRegisterCubit.close();
    _additionalDataCubit.close();
    super.dispose();
  }

  /// Check if returning from OTP verification and set to Step 3
  void _checkIfReturningFromOtp() {
    // Listen for route changes to detect return from OTP
    // In a real implementation, you might use route arguments or state management
    // For now, we'll add a method to manually trigger Step 3
  }

  /// Move to Step 3 after successful OTP verification
  void moveToStep3() {
    setState(() {
      _currentStep = 3;
    });

    // Initialize additional data cubit
    _additionalDataCubit.initialize();
  }

  // Validation for first step
  String? _validateIdCard(String? value, AppLocalizations l10n) {
    if (value == null || value.isEmpty) {
      return l10n.validateIdCardRequired;
    }
    return Validators.validateThaiIdCard(value, l10n);
  }

  // Validation for second step
  String? _validateUsername(String? value, AppLocalizations l10n) {
    return _validateRegisterCubit.validateField(
      fieldName: 'username',
      value: value ?? '',
      l10n: l10n,
    );
  }

  String? _validateEmail(String? value, AppLocalizations l10n) {
    return _validateRegisterCubit.validateField(
      fieldName: 'email',
      value: value ?? '',
      l10n: l10n,
    );
  }

  String? _validatePhone(String? value, AppLocalizations l10n) {
    return _validateRegisterCubit.validateField(
      fieldName: 'phone',
      value: value ?? '',
      l10n: l10n,
    );
  }

  String? _validatePassword(String? value, AppLocalizations l10n) {
    return _validateRegisterCubit.validateField(
      fieldName: 'password',
      value: value ?? '',
      l10n: l10n,
    );
  }

  String? _validateConfirmPassword(String? value, AppLocalizations l10n) {
    return _validateRegisterCubit.validateField(
      fieldName: 'confirmPassword',
      value: value ?? '',
      confirmValue: _passwordController.text,
      l10n: l10n,
    );
  }

  void _onButtonPressed() async {
    if (_formKey.currentState!.validate()) {
      if (_currentStep == 1) {
        final l10n = AppLocalizations.of(context)!;

        // Reset cubit state first
        _checkIdCardCubit.reset();

        // Validate ID Card and call API to check duplicate
        await _checkIdCardCubit.validateAndCheckIdCard(
          _idCardController.text,
          l10n,
        );

        // State handling is done through BlocListener in the build method
      } else if (_currentStep == 2) {
        // Validate registration form and check for duplicates
        await _validateRegistrationForm();
      } else if (_currentStep == 3) {
        // Validate additional data form
        await _validateAdditionalDataForm();
      }
    }
  }

  Future<void> _validateRegistrationForm() async {
    final l10n = AppLocalizations.of(context)!;

    // Don't reset here - let the validation handle state transitions
    // This prevents forceErrorText from disappearing during validation

    // Validate all fields including API duplicate check
    await _validateRegisterCubit.validateRegistrationForm(
      username: _usernameController.text,
      email: _emailController.text,
      phone: _phoneController.text,
      password: _passwordController.text,
      confirmPassword: _confirmPasswordController.text,
      l10n: l10n,
    );

    // State handling is done through BlocListener in the build method
  }

  void _onRegister() async {
    // Process the complete registration form
    // Navigate to OTP verification and wait for result
    final result = await context.router.push(
      OtpRoute(email: _emailController.text, referenceCode: 'xxxxx'),
    );

    // Check if OTP verification was successful
    if (result is Map &&
        result['success'] == true &&
        result['moveToStep3'] == true) {
      moveToStep3();
    }
  }

  Future<void> _validateAdditionalDataForm() async {
    final l10n = AppLocalizations.of(context)!;

    // Update cubit with current form values
    _additionalDataCubit.updateFirstName(_firstNameController.text);
    _additionalDataCubit.updateLastName(_lastNameController.text);
    _additionalDataCubit.updateDepartmentName(_departmentNameController.text);

    // Validate the form
    final isValid = _additionalDataCubit.validateForm(l10n);

    if (isValid) {
      // Complete registration process
      await _completeRegistration();
    }
  }

  Future<void> _completeRegistration() async {
    // Here you would combine all the data from steps 1, 2, and 3
    // and submit the complete registration

    final additionalData = _additionalDataCubit.getAdditionalData();
    if (additionalData != null) {
      // TODO: Implement complete registration API call
      // For now, just show success and navigate

      // Navigate to success page or main app
      context.router.replaceAll([const MemberWelcomeRoute()]);
    }
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;

    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBarCommon(
        title: _currentStep == 3 ? l10n.additionalDataTitle : l10n.register,
      ),
      body: SafeArea(
        child: MultiBlocListener(
          listeners: [
            BlocListener<CheckIdCardCubit, CheckIdCardState>(
              bloc: _checkIdCardCubit,
              listener: (context, state) {
                // Handle state changes when checking ID card
                if (_currentStep == 1) {
                  switch (state) {
                    case CheckIdCardValid():
                      // ID card is valid and not duplicate, move to second step
                      setState(() {
                        _currentStep = 2;
                      });
                      break;
                    default:
                    // No action needed for these states
                  }
                }
              },
            ),
            BlocListener<ValidateRegisterCubit, ValidateRegisterState>(
              bloc: _validateRegisterCubit,
              listener: (context, state) {
                // Handle state changes when validating registration
                if (_currentStep == 2) {
                  switch (state) {
                    case ValidateRegisterValid():
                      // All validations passed, proceed with registration
                      _onRegister();
                      break;
                    default:
                    // No action needed for these states
                  }
                }
              },
            ),
            BlocListener<AdditionalDataCubit, AdditionalDataState>(
              bloc: _additionalDataCubit,
              listener: (context, state) {
                // Handle state changes for additional data
                if (_currentStep == 3) {
                  switch (state) {
                    case AdditionalDataValid():
                      // Additional data is valid, complete registration
                      _completeRegistration();
                      break;
                    default:
                    // No action needed for these states
                  }
                }
              },
            ),
          ],
          child: Column(
            children: [
              // Main content
              Expanded(
                child: SingleChildScrollView(
                  child: Padding(
                    padding: EdgeInsets.symmetric(horizontal: 24.w),
                    child: Form(
                      key: _formKey,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          SizedBox(height: 24.h),

                          // Title based on current step
                          Text(
                            l10n.userInformation,
                            style: TextStyle(
                              fontFamily: AppFonts.notoSansThai,
                              fontSize: 20.sp,
                              fontWeight: FontWeight.w600,
                              color: AppColors.textDefaultDark,
                              height: 1.5.sp,
                            ),
                          ),

                          SizedBox(height: 24.h),

                          // Show fields based on current step
                          if (_currentStep == 1) ...[
                            // ID Card Input Field for first step
                            BlocBuilder<CheckIdCardCubit, CheckIdCardState>(
                              bloc: _checkIdCardCubit,
                              builder: (context, state) {
                                return CustomTextField(
                                  controller: _idCardController,
                                  label: l10n.idCardLabel,
                                  keyboardType: TextInputType.number,
                                  validator:
                                      (value) => _validateIdCard(value, l10n),
                                  isRequired: true,
                                  forceErrorText:
                                      _checkIdCardCubit.hasError
                                          ? _checkIdCardCubit.getErrorMessage(
                                            l10n,
                                          )
                                          : null,
                                );
                              },
                            ),
                          ] else if (_currentStep == 2) ...[
                            // Input fields for second step
                            BlocBuilder<
                              ValidateRegisterCubit,
                              ValidateRegisterState
                            >(
                              bloc: _validateRegisterCubit,
                              builder: (context, state) {
                                return CustomTextField(
                                  controller: _usernameController,
                                  label: l10n.usernameLabel,
                                  validator:
                                      (value) => _validateUsername(value, l10n),
                                  isRequired: true,
                                  textInputAction: TextInputAction.next,
                                  forceErrorText: _validateRegisterCubit
                                      .getFieldError('username', l10n),
                                  onChanged: (value) {
                                    // Clear field error when user starts typing
                                    _validateRegisterCubit.clearFieldError(
                                      'username',
                                    );
                                  },
                                );
                              },
                            ),
                            SizedBox(height: 24.h),

                            BlocBuilder<
                              ValidateRegisterCubit,
                              ValidateRegisterState
                            >(
                              bloc: _validateRegisterCubit,
                              builder: (context, state) {
                                return CustomTextField(
                                  controller: _emailController,
                                  label: l10n.emailLabel,
                                  keyboardType: TextInputType.emailAddress,
                                  validator:
                                      (value) => _validateEmail(value, l10n),
                                  isRequired: true,
                                  textInputAction: TextInputAction.next,
                                  forceErrorText: _validateRegisterCubit
                                      .getFieldError('email', l10n),
                                  onChanged: (value) {
                                    // Clear field error when user starts typing
                                    _validateRegisterCubit.clearFieldError(
                                      'email',
                                    );
                                  },
                                );
                              },
                            ),
                            SizedBox(height: 24.h),

                            BlocBuilder<
                              ValidateRegisterCubit,
                              ValidateRegisterState
                            >(
                              bloc: _validateRegisterCubit,
                              builder: (context, state) {
                                return CustomTextField(
                                  controller: _phoneController,
                                  label: l10n.phoneLabel,
                                  keyboardType: TextInputType.phone,
                                  validator:
                                      (value) => _validatePhone(value, l10n),
                                  isRequired: true,
                                  textInputAction: TextInputAction.next,
                                  forceErrorText: _validateRegisterCubit
                                      .getFieldError('phone', l10n),
                                  onChanged: (value) {
                                    // Clear field error when user starts typing
                                    _validateRegisterCubit.clearFieldError(
                                      'phone',
                                    );
                                  },
                                );
                              },
                            ),
                            SizedBox(height: 24.h),

                            CustomTextField(
                              controller: _passwordController,
                              label: l10n.passwordLabel,
                              obscureText: true,
                              validator:
                                  (value) => _validatePassword(value, l10n),
                              isRequired: true,
                              textInputAction: TextInputAction.next,
                            ),
                            SizedBox(height: 24.h),

                            CustomTextField(
                              controller: _confirmPasswordController,
                              label: l10n.confirmPasswordLabel,
                              obscureText: true,
                              validator:
                                  (value) =>
                                      _validateConfirmPassword(value, l10n),
                              isRequired: true,
                              textInputAction: TextInputAction.done,
                            ),
                          ] else if (_currentStep == 3) ...[
                            // Step 3: Additional Data Form
                            _buildStep3Form(l10n),
                          ],
                        ],
                      ),
                    ),
                  ),
                ),
              ),

              // Bottom button
              _buildBottomButton(l10n),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildBottomButton(AppLocalizations l10n) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, -1),
          ),
        ],
      ),
      child: BlocBuilder<CheckIdCardCubit, CheckIdCardState>(
        bloc: _checkIdCardCubit,
        builder: (context, checkIdCardState) {
          return BlocBuilder<ValidateRegisterCubit, ValidateRegisterState>(
            bloc: _validateRegisterCubit,
            builder: (context, validateRegisterState) {
              return BlocBuilder<AdditionalDataCubit, AdditionalDataState>(
                bloc: _additionalDataCubit,
                builder: (context, additionalDataState) {
                  final isCheckIdCardLoading =
                      checkIdCardState is CheckIdCardLoading;
                  final isValidateRegisterLoading =
                      validateRegisterState is ValidateRegisterLoading;
                  final isAdditionalDataLoading =
                      additionalDataState is AdditionalDataLoading;

                  final isLoading =
                      _currentStep == 1
                          ? isCheckIdCardLoading
                          : _currentStep == 2
                          ? isValidateRegisterLoading
                          : isAdditionalDataLoading;

                  return PrimaryButton(
                    text:
                        _currentStep == 1
                            ? l10n.confirm
                            : _currentStep == 2
                            ? l10n.register
                            : l10n.register,
                    width: 345.w,
                    height: 52.h,
                    borderRadius: 50,
                    onPressed: isLoading ? null : _onButtonPressed,
                    isLoading: isLoading,
                  );
                },
              );
            },
          );
        },
      ),
    );
  }

  /// Build Step 3 form fields
  Widget _buildStep3Form(AppLocalizations l10n) {
    return BlocBuilder<AdditionalDataCubit, AdditionalDataState>(
      bloc: _additionalDataCubit,
      builder: (context, state) {
        return Column(
          children: [
            // First Name
            CustomTextField(
              controller: _firstNameController,
              label: l10n.firstNameLabel,
              validator:
                  (value) => _validateStep3Field('firstName', value, l10n),
              isRequired: true,
              textInputAction: TextInputAction.next,
              forceErrorText: _additionalDataCubit.getFieldError('firstName'),
              onChanged: (value) {
                _additionalDataCubit.updateFirstName(value);
              },
            ),
            SizedBox(height: 24.h),

            // Last Name
            CustomTextField(
              controller: _lastNameController,
              label: l10n.lastNameLabel,
              validator:
                  (value) => _validateStep3Field('lastName', value, l10n),
              isRequired: true,
              textInputAction: TextInputAction.next,
              forceErrorText: _additionalDataCubit.getFieldError('lastName'),
              onChanged: (value) {
                _additionalDataCubit.updateLastName(value);
              },
            ),
            SizedBox(height: 24.h),

            // Member Type Dropdown
            CustomDropdownField<MemberType>(
              label: l10n.sectorTypeLabel,
              hintText: l10n.sectorTypeLabel,
              items: _additionalDataCubit.memberTypes,
              selectedValue: _additionalDataCubit.selectedMemberType,
              displayText: (memberType) => memberType.displayName,
              onChange: (memberType) {
                _additionalDataCubit.updateMemberType(memberType);
              },
              validator: () => _additionalDataCubit.getFieldError('sectorType'),
              isRequired: true,
            ),
            SizedBox(height: 24.h),

            // Government Agency Dropdown (only for government sector)
            if (_additionalDataCubit.selectedMemberType?.id == 1) ...[
              CustomDropdownField<GovernmentSector>(
                label: l10n.governmentAgencyLabel,
                hintText: l10n.governmentAgencyLabel,
                items: _additionalDataCubit.governmentAgencies,
                selectedValue: _additionalDataCubit.selectedGovernmentAgency,
                displayText: (sector) => sector.displayName,
                onChange: (sector) {
                  _additionalDataCubit.updateGovernmentAgency(sector);
                },
                validator:
                    () =>
                        _additionalDataCubit.getFieldError('governmentAgency'),
                isRequired: true,
              ),
              SizedBox(height: 24.h),

              // Ministry Dropdown (only when government sector is selected)
              if (_additionalDataCubit.selectedGovernmentAgency != null) ...[
                CustomDropdownField<Organization>(
                  label: l10n.ministryLabel,
                  hintText: l10n.ministryLabel,
                  items: _additionalDataCubit.ministries,
                  selectedValue: _additionalDataCubit.selectedMinistry,
                  displayText: (org) => org.displayName,
                  onChange: (org) {
                    _additionalDataCubit.updateMinistry(org);
                  },
                  validator: () => null, // Optional field
                  isRequired: false,
                ),
                SizedBox(height: 24.h),
              ],

              // Department Dropdown (only when ministry is selected)
              if (_additionalDataCubit.selectedMinistry != null) ...[
                CustomDropdownField<Organization>(
                  label: l10n.departmentLabel,
                  hintText: l10n.departmentLabel,
                  items: _additionalDataCubit.departments,
                  selectedValue: _additionalDataCubit.selectedDepartment,
                  displayText: (org) => org.displayName,
                  onChange: (org) {
                    _additionalDataCubit.updateDepartment(org);
                  },
                  validator: () => null, // Optional field
                  isRequired: false,
                ),
                SizedBox(height: 24.h),
              ],
            ],

            // Department Name
            CustomTextField(
              controller: _departmentNameController,
              label: l10n.departmentNameLabel,
              validator:
                  (value) => _validateStep3Field('departmentName', value, l10n),
              isRequired: true,
              textInputAction: TextInputAction.done,
              forceErrorText: _additionalDataCubit.getFieldError(
                'departmentName',
              ),
              onChanged: (value) {
                _additionalDataCubit.updateDepartmentName(value);
              },
            ),
          ],
        );
      },
    );
  }

  /// Validate Step 3 fields
  String? _validateStep3Field(
    String fieldName,
    String? value,
    AppLocalizations l10n,
  ) {
    if (value == null || value.trim().isEmpty) {
      switch (fieldName) {
        case 'firstName':
          return l10n.validateFirstNameRequired;
        case 'lastName':
          return l10n.validateLastNameRequired;
        case 'departmentName':
          return l10n.validateDepartmentNameRequired;
        default:
          return null;
      }
    }
    return null;
  }
}
