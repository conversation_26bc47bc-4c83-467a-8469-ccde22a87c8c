import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mcdc/core/constants/app_colors.dart';
import 'package:mcdc/core/constants/app_fonts.dart';
import 'package:mcdc/core/di/app_injector.dart';
import 'package:mcdc/shared/presentation/widgets/appbar/app_bar_common.dart';
import 'package:mcdc/shared/presentation/widgets/button/primary_button.dart';
import 'package:mcdc/shared/presentation/widgets/button/secondary_button.dart';
import 'package:mcdc/shared/presentation/widgets/dialog/dialog_confirm.dart';
import 'package:mcdc/shared/presentation/widgets/input/custom_text_field.dart';
import 'package:mcdc/shared/presentation/widgets/dialog/dialog_success.dart';
import 'package:mcdc/shared/presentation/widgets/loading/loading_overlay.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:mcdc/features/user/domain/entities/user_profile.dart';
import 'bloc/edit_profile_cubit.dart';
import 'bloc/edit_profile_state.dart';

@RoutePage()
class EditProfilePage extends StatelessWidget {
  const EditProfilePage({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider<EditProfileCubit>(
      create: (context) => AppInjector.get<EditProfileCubit>(),
      child: const _EditProfilePageContent(),
    );
  }
}

class _EditProfilePageContent extends StatefulWidget {
  const _EditProfilePageContent();

  @override
  State<_EditProfilePageContent> createState() => _EditProfilePageState();
}

class _EditProfilePageState extends State<_EditProfilePageContent> {
  final _formKey = GlobalKey<FormState>();

  // Controllers for form fields
  late final TextEditingController _usernameController;
  late final TextEditingController _firstNameController;
  late final TextEditingController _lastNameController;
  late final TextEditingController _idCardController;
  late final TextEditingController _emailController;
  late final TextEditingController _phoneController;
  late final TextEditingController _sectorTypeController;
  late final TextEditingController _organizationController;
  late final TextEditingController _websiteController;
  late final TextEditingController _passwordController;

  @override
  void initState() {
    super.initState();
    _initializeControllers();
    context.read<EditProfileCubit>().loadProfile();
  }

  void _initializeControllers() {
    _usernameController = TextEditingController();
    _firstNameController = TextEditingController();
    _lastNameController = TextEditingController();
    _idCardController = TextEditingController();
    _emailController = TextEditingController();
    _phoneController = TextEditingController();
    _sectorTypeController = TextEditingController();
    _organizationController = TextEditingController();
    _websiteController = TextEditingController();
    _passwordController = TextEditingController();
  }

  @override
  void dispose() {
    _usernameController.dispose();
    _firstNameController.dispose();
    _lastNameController.dispose();
    _idCardController.dispose();
    _emailController.dispose();
    _phoneController.dispose();
    _sectorTypeController.dispose();
    _organizationController.dispose();
    _websiteController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  void _populateFields(UserProfile profile) {
    _usernameController.text = profile.username;
    _firstNameController.text = profile.firstName;
    _lastNameController.text = profile.lastName;
    _idCardController.text = profile.idCardNumber;
    _emailController.text = profile.email;
    _phoneController.text = profile.phoneNumber;
    _sectorTypeController.text = profile.sectorType;
    _organizationController.text = profile.organizationName;
    _websiteController.text = profile.websiteName ?? '';
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;

    return Scaffold(
      backgroundColor: AppColors.backgroundDefault,
      appBar: AppBarCommon(title: l10n.editProfilePageTitle),
      body: BlocConsumer<EditProfileCubit, EditProfileState>(
        listener: (context, state) {
          if (state is EditProfileLoaded) {
            _populateFields(state.profile);
          } else if (state is EditProfileUpdateSuccess) {
            _showSuccessDialog(l10n);
          } else if (state is EditProfileError) {
            _showErrorSnackBar(state.message);
          }
        },
        builder: (context, state) {
          return LoadingOverlay(
            isLoading:
                state is EditProfileLoading || state is EditProfileUpdating,
            child: _buildBody(l10n, state),
          );
        },
      ),
    );
  }

  Widget _buildBody(AppLocalizations l10n, EditProfileState state) {
    return SingleChildScrollView(
      padding: EdgeInsets.all(16.w),
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // User Information Section
            _buildUserInformationSection(l10n),
            SizedBox(height: 24.h),

            // Organization Information Section
            _buildOrganizationInformationSection(l10n),
            SizedBox(height: 24.h),

            // Password Confirmation Section
            _buildPasswordConfirmationSection(l10n),
            SizedBox(height: 32.h),

            // Action Buttons
            _buildActionButtons(context, l10n),
            SizedBox(height: 24.h),
          ],
        ),
      ),
    );
  }

  Widget _buildUserInformationSection(AppLocalizations l10n) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          l10n.userInformationSection,
          style: TextStyle(
            fontFamily: AppFonts.notoSansThai,
            fontSize: 16.sp,
            fontWeight: FontWeight.w600,
            color: AppColors.textDefaultDark,
            height: 1.5,
          ),
        ),
        SizedBox(height: 16.h),

        // Username field (disabled)
        CustomTextField(
          controller: _usernameController,
          label: l10n.usernameLabel,
          enabled: false,
          readOnly: true,
          isRequired: true,
        ),
        SizedBox(height: 16.h),

        // First Name field
        CustomTextField(
          controller: _firstNameController,
          label: l10n.firstNameLabel,
          hintText: l10n.firstNameHint,
          isRequired: true,
          validator: (value) {
            if (value?.isEmpty ?? true) {
              return l10n.validateFirstNameRequired;
            }
            return null;
          },
        ),
        SizedBox(height: 16.h),

        // Last Name field
        CustomTextField(
          controller: _lastNameController,
          label: l10n.lastNameLabel,
          hintText: l10n.lastNameHint,
          isRequired: true,
          validator: (value) {
            if (value?.isEmpty ?? true) {
              return l10n.validateLastNameRequired;
            }
            return null;
          },
        ),
        SizedBox(height: 16.h),

        // ID Card Number field
        CustomTextField(
          controller: _idCardController,
          label: l10n.idCardNumberLabel,
          hintText: l10n.idCardNumberHint,
          keyboardType: TextInputType.number,
          isRequired: true,
          validator: (value) {
            if (value?.isEmpty ?? true) {
              return l10n.validateIdCardRequired;
            }
            if (value!.length != 13) {
              return l10n.validateIdCardLength;
            }
            return null;
          },
        ),
        SizedBox(height: 16.h),

        // Email field
        CustomTextField(
          controller: _emailController,
          label: l10n.emailLabel,
          hintText: l10n.emailHint,
          keyboardType: TextInputType.emailAddress,
          isRequired: true,
          validator: (value) {
            if (value?.isEmpty ?? true) {
              return l10n.validateEmailRequired;
            }
            if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value!)) {
              return l10n.validateEmailFormat;
            }
            return null;
          },
        ),
        SizedBox(height: 16.h),

        // Phone Number field
        CustomTextField(
          controller: _phoneController,
          label: l10n.phoneLabel,
          hintText: l10n.phoneNumberHint,
          keyboardType: TextInputType.phone,
          isRequired: true,
          validator: (value) {
            if (value?.isEmpty ?? true) {
              return l10n.validatePhoneRequired;
            }
            if (value!.length < 9 || value.length > 10) {
              return l10n.validatePhoneLength;
            }
            return null;
          },
        ),
      ],
    );
  }

  Widget _buildOrganizationInformationSection(AppLocalizations l10n) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          l10n.organizationInformationSection,
          style: TextStyle(
            fontFamily: AppFonts.notoSansThai,
            fontSize: 16.sp,
            fontWeight: FontWeight.w600,
            color: AppColors.textDefaultDark,
            height: 1.5,
          ),
        ),
        SizedBox(height: 16.h),

        // Sector Type field
        CustomTextField(
          controller: _sectorTypeController,
          label: l10n.sectorTypeLabel,
          hintText: l10n.sectorTypeHint,
          isRequired: true,
          validator: (value) {
            if (value?.isEmpty ?? true) {
              return l10n.validateSectorTypeRequired;
            }
            return null;
          },
        ),
        SizedBox(height: 16.h),

        // Organization Name field
        CustomTextField(
          controller: _organizationController,
          label: l10n.organizationNameLabel,
          hintText: l10n.organizationNameHintEdit,
          isRequired: true,
          validator: (value) {
            if (value?.isEmpty ?? true) {
              return l10n.validateOrganizationNameRequired;
            }
            return null;
          },
        ),
        SizedBox(height: 16.h),

        // Website Name field (optional)
        CustomTextField(
          controller: _websiteController,
          label: l10n.websiteNameLabel,
          hintText: l10n.websiteNameHint,
          isRequired: false,
        ),
      ],
    );
  }

  Widget _buildPasswordConfirmationSection(AppLocalizations l10n) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          l10n.passwordConfirmationSection,
          style: TextStyle(
            fontFamily: AppFonts.notoSansThai,
            fontSize: 16.sp,
            fontWeight: FontWeight.w600,
            color: AppColors.textDefaultDark,
            height: 1.5,
          ),
        ),
        SizedBox(height: 16.h),

        // Current Password field
        CustomTextField(
          controller: _passwordController,
          label: l10n.currentPasswordLabel,
          hintText: l10n.currentPasswordHint,
          obscureText: true,
          isRequired: true,
          validator: (value) {
            if (value?.isEmpty ?? true) {
              return l10n.validateCurrentPasswordRequired;
            }
            return null;
          },
        ),
      ],
    );
  }

  Widget _buildActionButtons(BuildContext context, AppLocalizations l10n) {
    return Row(
      children: [
        Expanded(
          child: SecondaryButton(
            text: l10n.cancel,
            onPressed: () => context.router.back(),
          ),
        ),
        SizedBox(width: 16.w),
        Expanded(
          child: PrimaryButton(
            text: l10n.saveButton,
            onPressed: () => _onSavePressed(context, l10n),
          ),
        ),
      ],
    );
  }

  void _onSavePressed(BuildContext context, AppLocalizations l10n) async {
    if (_formKey.currentState?.validate() ?? false) {
      final confirmed = await _showConfirmationDialog(context, l10n);
      if (!confirmed) return;
      if (confirmed && context.mounted) {
        context.read<EditProfileCubit>().updateProfile(
          firstName: _firstNameController.text.trim(),
          lastName: _lastNameController.text.trim(),
          idCardNumber: _idCardController.text.trim(),
          email: _emailController.text.trim(),
          phoneNumber: _phoneController.text.trim(),
          sectorType: _sectorTypeController.text.trim(),
          organizationName: _organizationController.text.trim(),
          websiteName:
              _websiteController.text.trim().isEmpty
                  ? null
                  : _websiteController.text.trim(),
          currentPassword: _passwordController.text,
        );
      }
    }
  }

  Future<bool> _showConfirmationDialog(
    BuildContext context,
    AppLocalizations l10n,
  ) async {
    final confirmed = await DialogConfirm.show(
      context: context,
      title: l10n.confirmChangeProfile,
      message: l10n.confirmChangeProfileMessage,
    );

    return confirmed ?? false;
  }

  void _showSuccessDialog(AppLocalizations l10n) {
    DialogSuccess.show(
      context: context,
      afterDismiss: () {
        context.read<EditProfileCubit>().resetToLoaded();
        _passwordController.clear();
        context.router.back();
      },
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message), backgroundColor: AppColors.critical),
    );
  }
}
