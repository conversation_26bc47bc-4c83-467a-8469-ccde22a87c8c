import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:mcdc/core/constants/app_colors.dart';
import 'package:mcdc/core/di/app_injector.dart';
import 'package:mcdc/shared/presentation/widgets/button/primary_button.dart';
import 'package:mcdc/shared/presentation/widgets/input/custom_text_field.dart';
import 'bloc/user_login_cubit.dart';
import 'bloc/user_login_state.dart';

@RoutePage()
class LoginPage extends StatelessWidget {
  const LoginPage({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => AppInjector.get<UserLoginCubit>(),
      child: const _LoginPageContent(),
    );
  }
}

class _LoginPageContent extends StatefulWidget {
  const _LoginPageContent();

  @override
  State<_LoginPageContent> createState() => _LoginPageContentState();
}

class _LoginPageContentState extends State<_LoginPageContent> {
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>(
    debugLabel: 'LoginPage',
  );

  final TextEditingController _usernameController = TextEditingController();
  final TextEditingController _passwordController = TextEditingController();
  bool _obscurePassword = true;

  @override
  void dispose() {
    _usernameController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;

    return Scaffold(
      key: _scaffoldKey,
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: SvgPicture.asset(
            'assets/icons/arrow-left.svg',
            colorFilter: const ColorFilter.mode(
              AppColors.textDefaultDark,
              BlendMode.srcIn,
            ),
          ),
          onPressed: () => context.router.pop(),
        ),
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: 24.w),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                24.h.verticalSpace,
                Text(
                  l10n.login,
                  style: TextStyle(
                    fontSize: 24.sp,
                    fontWeight: FontWeight.w600,
                    color: AppColors.textDefaultDark,
                    height: 1.5,
                  ),
                ),
                8.h.verticalSpace,
                Text(
                  l10n.loginDescription,
                  style: TextStyle(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.w400,
                    color: AppColors.textDefaultDark,
                    height: 1.5,
                  ),
                ),
                48.h.verticalSpace,
                CustomTextField(
                  controller: _usernameController,
                  label: l10n.usernameLabel,
                  isRequired: true,
                ),
                16.h.verticalSpace,
                CustomTextField(
                  controller: _passwordController,
                  label: l10n.passwordLabel,
                  isRequired: true,
                  obscureText: _obscurePassword,
                  suffixIcon: GestureDetector(
                    onTap: () {
                      setState(() {
                        _obscurePassword = !_obscurePassword;
                      });
                    },
                    child: Container(
                      height: 32.w,
                      width: 32.w,
                      decoration: BoxDecoration(color: Colors.transparent),
                      child: Icon(
                        _obscurePassword
                            ? Icons.visibility_off_outlined
                            : Icons.visibility_outlined,
                        size: 16.w,
                        color: AppColors.iconDefault,
                      ),
                    ),
                  ),
                ),
                16.h.verticalSpace,
                Align(
                  alignment: Alignment.centerRight,
                  child: GestureDetector(
                    onTap: () {
                      // Implement forgot password functionality
                    },
                    child: Text(
                      l10n.forgotPassword,
                      style: TextStyle(
                        fontSize: 16.sp,
                        fontWeight: FontWeight.w600,
                        color: AppColors.textPrimary,
                        height: 1.5,
                      ),
                    ),
                  ),
                ),
                24.h.verticalSpace,
                BlocConsumer<UserLoginCubit, UserLoginState>(
                  listener: (context, state) {
                    final cubit = context.read<UserLoginCubit>();

                    if (state.toString().contains('success')) {
                      // Navigate to main page on successful login
                      context.router.pushPath('/main');
                    } else if (cubit.isError) {
                      // Show error message
                      final errorMessage =
                          cubit.errorMessage ?? 'เกิดข้อผิดพลาด';
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text(errorMessage),
                          backgroundColor: Colors.red,
                          action: SnackBarAction(
                            label: 'ปิด',
                            textColor: Colors.white,
                            onPressed: () {
                              ScaffoldMessenger.of(
                                context,
                              ).hideCurrentSnackBar();
                            },
                          ),
                        ),
                      );
                    }
                  },
                  builder: (context, state) {
                    final cubit = context.read<UserLoginCubit>();

                    return PrimaryButton(
                      text: l10n.login,
                      width: double.infinity,
                      height: 52.h,
                      borderRadius: 50.r,
                      isLoading: cubit.isLoading,
                      onPressed:
                          cubit.isLoading
                              ? null
                              : () {
                                final username = _usernameController.text;
                                final password = _passwordController.text;

                                cubit.login(
                                  username: username,
                                  password: password,
                                );
                              },
                    );
                  },
                ),
                32.h.verticalSpace,
                Row(
                  children: [
                    Expanded(
                      child: Container(
                        height: 1,
                        color: AppColors.dividerColor,
                      ),
                    ),
                    Padding(
                      padding: EdgeInsets.symmetric(horizontal: 16.w),
                      child: Text(
                        l10n.or,
                        style: TextStyle(
                          fontSize: 16.sp,
                          fontWeight: FontWeight.w400,
                          color: AppColors.textDefaultDark,
                          height: 1.5,
                        ),
                      ),
                    ),
                    Expanded(
                      child: Container(
                        height: 1,
                        color: AppColors.dividerColor,
                      ),
                    ),
                  ],
                ),
                24.h.verticalSpace,
                Container(
                  width: double.infinity,
                  height: 52.h,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(50.r),
                    border: Border.all(color: AppColors.dividerColor),
                  ),
                  child: TextButton(
                    onPressed: () {
                      context.router.pushPath('/how-to-login-thai-id');
                    },
                    style: TextButton.styleFrom(
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(50.r),
                      ),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Image.asset(
                          'assets/icons/thaid-logo.png',
                          width: 24.w,
                          height: 24.h,
                        ),
                        SizedBox(width: 8.w),
                        Text(
                          l10n.loginWithThaID,
                          style: TextStyle(
                            fontSize: 16.sp,
                            fontWeight: FontWeight.w400,
                            color: const Color(0xFF0A3A57),
                            height: 1.5,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                24.h.verticalSpace,
                Center(
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        l10n.noAccountYet,
                        style: TextStyle(
                          fontSize: 16.sp,
                          fontWeight: FontWeight.w400,
                          color: AppColors.textDefault,
                          height: 1.5,
                        ),
                      ),
                      8.w.horizontalSpace,
                      GestureDetector(
                        onTap: () {
                          context.router.pushPath('/privacy-policy');
                        },
                        child: Text(
                          l10n.register,
                          style: TextStyle(
                            fontSize: 16.sp,
                            fontWeight: FontWeight.w400,
                            color: AppColors.textPrimary,
                            height: 1.5,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                24.h.verticalSpace,
              ],
            ),
          ),
        ),
      ),
    );
  }
}
