import 'package:freezed_annotation/freezed_annotation.dart';
import '../../domain/entities/user.dart';
import 'package:mcdc/core/enums/user_type.dart';

part 'user_model.freezed.dart';
part 'user_model.g.dart';

@freezed
abstract class UserModel with _$UserModel {
  const factory UserModel({
    required int id,
    required String name,
    @JsonKey(name: 'first_name') required String firstName,
    @JsonKey(name: 'last_name') required String lastName,
    required String email,
    required String phone,
    @Json<PERSON><PERSON>(name: 'identity_card_no') String? identityCardNo,
    required String username,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'app_mas_member_type_id') required int appMasMemberTypeId,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'app_mas_member_type_display')
    required String appMasMemberTypeDisplay,
    @<PERSON>son<PERSON><PERSON>(name: 'app_mas_government_sector_id')
    int? appMasGovernmentSectorId,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'app_mas_government_sector_display')
    required String appMasGovernmentSectorDisplay,
    @JsonK<PERSON>(name: 'app_mas_ministry_id') int? appMasMinistryId,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'app_mas_ministry_display')
    required String appMasMinistryDisplay,
    @JsonKey(name: 'app_mas_department_id') int? appMasDepartmentId,
    @JsonKey(name: 'app_mas_department_display')
    required String appMasDepartmentDisplay,
    String? website,
    @JsonKey(name: 'create_date') required DateTime createDate,
    @JsonKey(name: 'is_notification') required String isNotification,
    required String status,
    @JsonKey(name: 'status_display') required String statusDisplay,
    required String lang,
    @JsonKey(
      name: 'user_type',
      fromJson: UserType.fromJson,
      toJson: UserType.toJson,
    )
    required UserType userType,
  }) = _UserModel;

  factory UserModel.fromJson(Map<String, dynamic> json) =>
      _$UserModelFromJson(json);

  static List<UserModel> fromJsonList(List<Map<String, dynamic>> jsonList) =>
      jsonList.map((e) => UserModel.fromJson(e)).toList();
}

extension UserModelX on UserModel {
  User toEntity() => User(
    id: id,
    name: name,
    firstName: firstName,
    lastName: lastName,
    email: email,
    phone: phone,
    identityCardNo: identityCardNo,
    username: username,
    appMasMemberTypeId: appMasMemberTypeId,
    appMasMemberTypeDisplay: appMasMemberTypeDisplay,
    appMasGovernmentSectorId: appMasGovernmentSectorId,
    appMasGovernmentSectorDisplay: appMasGovernmentSectorDisplay,
    appMasMinistryId: appMasMinistryId,
    appMasMinistryDisplay: appMasMinistryDisplay,
    appMasDepartmentId: appMasDepartmentId,
    appMasDepartmentDisplay: appMasDepartmentDisplay,
    website: website,
    createDate: createDate,
    isNotification: isNotification,
    status: status,
    statusDisplay: statusDisplay,
    lang: lang,
    userType: userType,
  );
}
