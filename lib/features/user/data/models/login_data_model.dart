import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:mcdc/features/user/data/models/user_model.dart';
import '../../domain/entities/login_data.dart';
import 'token_model.dart';
import 'session_info_model.dart';

part 'login_data_model.freezed.dart';
part 'login_data_model.g.dart';

@freezed
abstract class LoginDataModel with _$LoginDataModel {
  const factory LoginDataModel({
    required UserModel user,
    required TokenModel tokens,
    @JsonKey(name: 'session_info') required SessionInfoModel sessionInfo,
  }) = _LoginDataModel;

  factory LoginDataModel.fromJson(Map<String, dynamic> json) =>
      _$LoginDataModel<PERSON>romJson(json);
}

extension LoginDataModelX on LoginDataModel {
  LoginData toEntity() => LoginData(
    user: user.toEntity(),
    tokens: tokens.toEntity(),
    sessionInfo: sessionInfo.toEntity(),
  );
}
