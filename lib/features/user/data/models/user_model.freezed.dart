// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'user_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$UserModel {

 int get id; String get name;@JsonKey(name: 'first_name') String get firstName;@JsonKey(name: 'last_name') String get lastName; String get email; String get phone;@JsonKey(name: 'identity_card_no') String? get identityCardNo; String get username;@JsonKey(name: 'app_mas_member_type_id') int get appMasMemberTypeId;@JsonKey(name: 'app_mas_member_type_display') String get appMasMemberTypeDisplay;@JsonKey(name: 'app_mas_government_sector_id') int? get appMasGovernmentSectorId;@JsonKey(name: 'app_mas_government_sector_display') String get appMasGovernmentSectorDisplay;@JsonKey(name: 'app_mas_ministry_id') int? get appMasMinistryId;@JsonKey(name: 'app_mas_ministry_display') String get appMasMinistryDisplay;@JsonKey(name: 'app_mas_department_id') int? get appMasDepartmentId;@JsonKey(name: 'app_mas_department_display') String get appMasDepartmentDisplay; String? get website;@JsonKey(name: 'create_date') DateTime get createDate;@JsonKey(name: 'is_notification') String get isNotification; String get status;@JsonKey(name: 'status_display') String get statusDisplay; String get lang;@JsonKey(name: 'user_type', fromJson: UserType.fromJson, toJson: UserType.toJson) UserType get userType;
/// Create a copy of UserModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$UserModelCopyWith<UserModel> get copyWith => _$UserModelCopyWithImpl<UserModel>(this as UserModel, _$identity);

  /// Serializes this UserModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is UserModel&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&(identical(other.firstName, firstName) || other.firstName == firstName)&&(identical(other.lastName, lastName) || other.lastName == lastName)&&(identical(other.email, email) || other.email == email)&&(identical(other.phone, phone) || other.phone == phone)&&(identical(other.identityCardNo, identityCardNo) || other.identityCardNo == identityCardNo)&&(identical(other.username, username) || other.username == username)&&(identical(other.appMasMemberTypeId, appMasMemberTypeId) || other.appMasMemberTypeId == appMasMemberTypeId)&&(identical(other.appMasMemberTypeDisplay, appMasMemberTypeDisplay) || other.appMasMemberTypeDisplay == appMasMemberTypeDisplay)&&(identical(other.appMasGovernmentSectorId, appMasGovernmentSectorId) || other.appMasGovernmentSectorId == appMasGovernmentSectorId)&&(identical(other.appMasGovernmentSectorDisplay, appMasGovernmentSectorDisplay) || other.appMasGovernmentSectorDisplay == appMasGovernmentSectorDisplay)&&(identical(other.appMasMinistryId, appMasMinistryId) || other.appMasMinistryId == appMasMinistryId)&&(identical(other.appMasMinistryDisplay, appMasMinistryDisplay) || other.appMasMinistryDisplay == appMasMinistryDisplay)&&(identical(other.appMasDepartmentId, appMasDepartmentId) || other.appMasDepartmentId == appMasDepartmentId)&&(identical(other.appMasDepartmentDisplay, appMasDepartmentDisplay) || other.appMasDepartmentDisplay == appMasDepartmentDisplay)&&(identical(other.website, website) || other.website == website)&&(identical(other.createDate, createDate) || other.createDate == createDate)&&(identical(other.isNotification, isNotification) || other.isNotification == isNotification)&&(identical(other.status, status) || other.status == status)&&(identical(other.statusDisplay, statusDisplay) || other.statusDisplay == statusDisplay)&&(identical(other.lang, lang) || other.lang == lang)&&(identical(other.userType, userType) || other.userType == userType));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hashAll([runtimeType,id,name,firstName,lastName,email,phone,identityCardNo,username,appMasMemberTypeId,appMasMemberTypeDisplay,appMasGovernmentSectorId,appMasGovernmentSectorDisplay,appMasMinistryId,appMasMinistryDisplay,appMasDepartmentId,appMasDepartmentDisplay,website,createDate,isNotification,status,statusDisplay,lang,userType]);

@override
String toString() {
  return 'UserModel(id: $id, name: $name, firstName: $firstName, lastName: $lastName, email: $email, phone: $phone, identityCardNo: $identityCardNo, username: $username, appMasMemberTypeId: $appMasMemberTypeId, appMasMemberTypeDisplay: $appMasMemberTypeDisplay, appMasGovernmentSectorId: $appMasGovernmentSectorId, appMasGovernmentSectorDisplay: $appMasGovernmentSectorDisplay, appMasMinistryId: $appMasMinistryId, appMasMinistryDisplay: $appMasMinistryDisplay, appMasDepartmentId: $appMasDepartmentId, appMasDepartmentDisplay: $appMasDepartmentDisplay, website: $website, createDate: $createDate, isNotification: $isNotification, status: $status, statusDisplay: $statusDisplay, lang: $lang, userType: $userType)';
}


}

/// @nodoc
abstract mixin class $UserModelCopyWith<$Res>  {
  factory $UserModelCopyWith(UserModel value, $Res Function(UserModel) _then) = _$UserModelCopyWithImpl;
@useResult
$Res call({
 int id, String name,@JsonKey(name: 'first_name') String firstName,@JsonKey(name: 'last_name') String lastName, String email, String phone,@JsonKey(name: 'identity_card_no') String? identityCardNo, String username,@JsonKey(name: 'app_mas_member_type_id') int appMasMemberTypeId,@JsonKey(name: 'app_mas_member_type_display') String appMasMemberTypeDisplay,@JsonKey(name: 'app_mas_government_sector_id') int? appMasGovernmentSectorId,@JsonKey(name: 'app_mas_government_sector_display') String appMasGovernmentSectorDisplay,@JsonKey(name: 'app_mas_ministry_id') int? appMasMinistryId,@JsonKey(name: 'app_mas_ministry_display') String appMasMinistryDisplay,@JsonKey(name: 'app_mas_department_id') int? appMasDepartmentId,@JsonKey(name: 'app_mas_department_display') String appMasDepartmentDisplay, String? website,@JsonKey(name: 'create_date') DateTime createDate,@JsonKey(name: 'is_notification') String isNotification, String status,@JsonKey(name: 'status_display') String statusDisplay, String lang,@JsonKey(name: 'user_type', fromJson: UserType.fromJson, toJson: UserType.toJson) UserType userType
});




}
/// @nodoc
class _$UserModelCopyWithImpl<$Res>
    implements $UserModelCopyWith<$Res> {
  _$UserModelCopyWithImpl(this._self, this._then);

  final UserModel _self;
  final $Res Function(UserModel) _then;

/// Create a copy of UserModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? name = null,Object? firstName = null,Object? lastName = null,Object? email = null,Object? phone = null,Object? identityCardNo = freezed,Object? username = null,Object? appMasMemberTypeId = null,Object? appMasMemberTypeDisplay = null,Object? appMasGovernmentSectorId = freezed,Object? appMasGovernmentSectorDisplay = null,Object? appMasMinistryId = freezed,Object? appMasMinistryDisplay = null,Object? appMasDepartmentId = freezed,Object? appMasDepartmentDisplay = null,Object? website = freezed,Object? createDate = null,Object? isNotification = null,Object? status = null,Object? statusDisplay = null,Object? lang = null,Object? userType = null,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,firstName: null == firstName ? _self.firstName : firstName // ignore: cast_nullable_to_non_nullable
as String,lastName: null == lastName ? _self.lastName : lastName // ignore: cast_nullable_to_non_nullable
as String,email: null == email ? _self.email : email // ignore: cast_nullable_to_non_nullable
as String,phone: null == phone ? _self.phone : phone // ignore: cast_nullable_to_non_nullable
as String,identityCardNo: freezed == identityCardNo ? _self.identityCardNo : identityCardNo // ignore: cast_nullable_to_non_nullable
as String?,username: null == username ? _self.username : username // ignore: cast_nullable_to_non_nullable
as String,appMasMemberTypeId: null == appMasMemberTypeId ? _self.appMasMemberTypeId : appMasMemberTypeId // ignore: cast_nullable_to_non_nullable
as int,appMasMemberTypeDisplay: null == appMasMemberTypeDisplay ? _self.appMasMemberTypeDisplay : appMasMemberTypeDisplay // ignore: cast_nullable_to_non_nullable
as String,appMasGovernmentSectorId: freezed == appMasGovernmentSectorId ? _self.appMasGovernmentSectorId : appMasGovernmentSectorId // ignore: cast_nullable_to_non_nullable
as int?,appMasGovernmentSectorDisplay: null == appMasGovernmentSectorDisplay ? _self.appMasGovernmentSectorDisplay : appMasGovernmentSectorDisplay // ignore: cast_nullable_to_non_nullable
as String,appMasMinistryId: freezed == appMasMinistryId ? _self.appMasMinistryId : appMasMinistryId // ignore: cast_nullable_to_non_nullable
as int?,appMasMinistryDisplay: null == appMasMinistryDisplay ? _self.appMasMinistryDisplay : appMasMinistryDisplay // ignore: cast_nullable_to_non_nullable
as String,appMasDepartmentId: freezed == appMasDepartmentId ? _self.appMasDepartmentId : appMasDepartmentId // ignore: cast_nullable_to_non_nullable
as int?,appMasDepartmentDisplay: null == appMasDepartmentDisplay ? _self.appMasDepartmentDisplay : appMasDepartmentDisplay // ignore: cast_nullable_to_non_nullable
as String,website: freezed == website ? _self.website : website // ignore: cast_nullable_to_non_nullable
as String?,createDate: null == createDate ? _self.createDate : createDate // ignore: cast_nullable_to_non_nullable
as DateTime,isNotification: null == isNotification ? _self.isNotification : isNotification // ignore: cast_nullable_to_non_nullable
as String,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as String,statusDisplay: null == statusDisplay ? _self.statusDisplay : statusDisplay // ignore: cast_nullable_to_non_nullable
as String,lang: null == lang ? _self.lang : lang // ignore: cast_nullable_to_non_nullable
as String,userType: null == userType ? _self.userType : userType // ignore: cast_nullable_to_non_nullable
as UserType,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _UserModel implements UserModel {
  const _UserModel({required this.id, required this.name, @JsonKey(name: 'first_name') required this.firstName, @JsonKey(name: 'last_name') required this.lastName, required this.email, required this.phone, @JsonKey(name: 'identity_card_no') this.identityCardNo, required this.username, @JsonKey(name: 'app_mas_member_type_id') required this.appMasMemberTypeId, @JsonKey(name: 'app_mas_member_type_display') required this.appMasMemberTypeDisplay, @JsonKey(name: 'app_mas_government_sector_id') this.appMasGovernmentSectorId, @JsonKey(name: 'app_mas_government_sector_display') required this.appMasGovernmentSectorDisplay, @JsonKey(name: 'app_mas_ministry_id') this.appMasMinistryId, @JsonKey(name: 'app_mas_ministry_display') required this.appMasMinistryDisplay, @JsonKey(name: 'app_mas_department_id') this.appMasDepartmentId, @JsonKey(name: 'app_mas_department_display') required this.appMasDepartmentDisplay, this.website, @JsonKey(name: 'create_date') required this.createDate, @JsonKey(name: 'is_notification') required this.isNotification, required this.status, @JsonKey(name: 'status_display') required this.statusDisplay, required this.lang, @JsonKey(name: 'user_type', fromJson: UserType.fromJson, toJson: UserType.toJson) required this.userType});
  factory _UserModel.fromJson(Map<String, dynamic> json) => _$UserModelFromJson(json);

@override final  int id;
@override final  String name;
@override@JsonKey(name: 'first_name') final  String firstName;
@override@JsonKey(name: 'last_name') final  String lastName;
@override final  String email;
@override final  String phone;
@override@JsonKey(name: 'identity_card_no') final  String? identityCardNo;
@override final  String username;
@override@JsonKey(name: 'app_mas_member_type_id') final  int appMasMemberTypeId;
@override@JsonKey(name: 'app_mas_member_type_display') final  String appMasMemberTypeDisplay;
@override@JsonKey(name: 'app_mas_government_sector_id') final  int? appMasGovernmentSectorId;
@override@JsonKey(name: 'app_mas_government_sector_display') final  String appMasGovernmentSectorDisplay;
@override@JsonKey(name: 'app_mas_ministry_id') final  int? appMasMinistryId;
@override@JsonKey(name: 'app_mas_ministry_display') final  String appMasMinistryDisplay;
@override@JsonKey(name: 'app_mas_department_id') final  int? appMasDepartmentId;
@override@JsonKey(name: 'app_mas_department_display') final  String appMasDepartmentDisplay;
@override final  String? website;
@override@JsonKey(name: 'create_date') final  DateTime createDate;
@override@JsonKey(name: 'is_notification') final  String isNotification;
@override final  String status;
@override@JsonKey(name: 'status_display') final  String statusDisplay;
@override final  String lang;
@override@JsonKey(name: 'user_type', fromJson: UserType.fromJson, toJson: UserType.toJson) final  UserType userType;

/// Create a copy of UserModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$UserModelCopyWith<_UserModel> get copyWith => __$UserModelCopyWithImpl<_UserModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$UserModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _UserModel&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&(identical(other.firstName, firstName) || other.firstName == firstName)&&(identical(other.lastName, lastName) || other.lastName == lastName)&&(identical(other.email, email) || other.email == email)&&(identical(other.phone, phone) || other.phone == phone)&&(identical(other.identityCardNo, identityCardNo) || other.identityCardNo == identityCardNo)&&(identical(other.username, username) || other.username == username)&&(identical(other.appMasMemberTypeId, appMasMemberTypeId) || other.appMasMemberTypeId == appMasMemberTypeId)&&(identical(other.appMasMemberTypeDisplay, appMasMemberTypeDisplay) || other.appMasMemberTypeDisplay == appMasMemberTypeDisplay)&&(identical(other.appMasGovernmentSectorId, appMasGovernmentSectorId) || other.appMasGovernmentSectorId == appMasGovernmentSectorId)&&(identical(other.appMasGovernmentSectorDisplay, appMasGovernmentSectorDisplay) || other.appMasGovernmentSectorDisplay == appMasGovernmentSectorDisplay)&&(identical(other.appMasMinistryId, appMasMinistryId) || other.appMasMinistryId == appMasMinistryId)&&(identical(other.appMasMinistryDisplay, appMasMinistryDisplay) || other.appMasMinistryDisplay == appMasMinistryDisplay)&&(identical(other.appMasDepartmentId, appMasDepartmentId) || other.appMasDepartmentId == appMasDepartmentId)&&(identical(other.appMasDepartmentDisplay, appMasDepartmentDisplay) || other.appMasDepartmentDisplay == appMasDepartmentDisplay)&&(identical(other.website, website) || other.website == website)&&(identical(other.createDate, createDate) || other.createDate == createDate)&&(identical(other.isNotification, isNotification) || other.isNotification == isNotification)&&(identical(other.status, status) || other.status == status)&&(identical(other.statusDisplay, statusDisplay) || other.statusDisplay == statusDisplay)&&(identical(other.lang, lang) || other.lang == lang)&&(identical(other.userType, userType) || other.userType == userType));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hashAll([runtimeType,id,name,firstName,lastName,email,phone,identityCardNo,username,appMasMemberTypeId,appMasMemberTypeDisplay,appMasGovernmentSectorId,appMasGovernmentSectorDisplay,appMasMinistryId,appMasMinistryDisplay,appMasDepartmentId,appMasDepartmentDisplay,website,createDate,isNotification,status,statusDisplay,lang,userType]);

@override
String toString() {
  return 'UserModel(id: $id, name: $name, firstName: $firstName, lastName: $lastName, email: $email, phone: $phone, identityCardNo: $identityCardNo, username: $username, appMasMemberTypeId: $appMasMemberTypeId, appMasMemberTypeDisplay: $appMasMemberTypeDisplay, appMasGovernmentSectorId: $appMasGovernmentSectorId, appMasGovernmentSectorDisplay: $appMasGovernmentSectorDisplay, appMasMinistryId: $appMasMinistryId, appMasMinistryDisplay: $appMasMinistryDisplay, appMasDepartmentId: $appMasDepartmentId, appMasDepartmentDisplay: $appMasDepartmentDisplay, website: $website, createDate: $createDate, isNotification: $isNotification, status: $status, statusDisplay: $statusDisplay, lang: $lang, userType: $userType)';
}


}

/// @nodoc
abstract mixin class _$UserModelCopyWith<$Res> implements $UserModelCopyWith<$Res> {
  factory _$UserModelCopyWith(_UserModel value, $Res Function(_UserModel) _then) = __$UserModelCopyWithImpl;
@override @useResult
$Res call({
 int id, String name,@JsonKey(name: 'first_name') String firstName,@JsonKey(name: 'last_name') String lastName, String email, String phone,@JsonKey(name: 'identity_card_no') String? identityCardNo, String username,@JsonKey(name: 'app_mas_member_type_id') int appMasMemberTypeId,@JsonKey(name: 'app_mas_member_type_display') String appMasMemberTypeDisplay,@JsonKey(name: 'app_mas_government_sector_id') int? appMasGovernmentSectorId,@JsonKey(name: 'app_mas_government_sector_display') String appMasGovernmentSectorDisplay,@JsonKey(name: 'app_mas_ministry_id') int? appMasMinistryId,@JsonKey(name: 'app_mas_ministry_display') String appMasMinistryDisplay,@JsonKey(name: 'app_mas_department_id') int? appMasDepartmentId,@JsonKey(name: 'app_mas_department_display') String appMasDepartmentDisplay, String? website,@JsonKey(name: 'create_date') DateTime createDate,@JsonKey(name: 'is_notification') String isNotification, String status,@JsonKey(name: 'status_display') String statusDisplay, String lang,@JsonKey(name: 'user_type', fromJson: UserType.fromJson, toJson: UserType.toJson) UserType userType
});




}
/// @nodoc
class __$UserModelCopyWithImpl<$Res>
    implements _$UserModelCopyWith<$Res> {
  __$UserModelCopyWithImpl(this._self, this._then);

  final _UserModel _self;
  final $Res Function(_UserModel) _then;

/// Create a copy of UserModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? name = null,Object? firstName = null,Object? lastName = null,Object? email = null,Object? phone = null,Object? identityCardNo = freezed,Object? username = null,Object? appMasMemberTypeId = null,Object? appMasMemberTypeDisplay = null,Object? appMasGovernmentSectorId = freezed,Object? appMasGovernmentSectorDisplay = null,Object? appMasMinistryId = freezed,Object? appMasMinistryDisplay = null,Object? appMasDepartmentId = freezed,Object? appMasDepartmentDisplay = null,Object? website = freezed,Object? createDate = null,Object? isNotification = null,Object? status = null,Object? statusDisplay = null,Object? lang = null,Object? userType = null,}) {
  return _then(_UserModel(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,firstName: null == firstName ? _self.firstName : firstName // ignore: cast_nullable_to_non_nullable
as String,lastName: null == lastName ? _self.lastName : lastName // ignore: cast_nullable_to_non_nullable
as String,email: null == email ? _self.email : email // ignore: cast_nullable_to_non_nullable
as String,phone: null == phone ? _self.phone : phone // ignore: cast_nullable_to_non_nullable
as String,identityCardNo: freezed == identityCardNo ? _self.identityCardNo : identityCardNo // ignore: cast_nullable_to_non_nullable
as String?,username: null == username ? _self.username : username // ignore: cast_nullable_to_non_nullable
as String,appMasMemberTypeId: null == appMasMemberTypeId ? _self.appMasMemberTypeId : appMasMemberTypeId // ignore: cast_nullable_to_non_nullable
as int,appMasMemberTypeDisplay: null == appMasMemberTypeDisplay ? _self.appMasMemberTypeDisplay : appMasMemberTypeDisplay // ignore: cast_nullable_to_non_nullable
as String,appMasGovernmentSectorId: freezed == appMasGovernmentSectorId ? _self.appMasGovernmentSectorId : appMasGovernmentSectorId // ignore: cast_nullable_to_non_nullable
as int?,appMasGovernmentSectorDisplay: null == appMasGovernmentSectorDisplay ? _self.appMasGovernmentSectorDisplay : appMasGovernmentSectorDisplay // ignore: cast_nullable_to_non_nullable
as String,appMasMinistryId: freezed == appMasMinistryId ? _self.appMasMinistryId : appMasMinistryId // ignore: cast_nullable_to_non_nullable
as int?,appMasMinistryDisplay: null == appMasMinistryDisplay ? _self.appMasMinistryDisplay : appMasMinistryDisplay // ignore: cast_nullable_to_non_nullable
as String,appMasDepartmentId: freezed == appMasDepartmentId ? _self.appMasDepartmentId : appMasDepartmentId // ignore: cast_nullable_to_non_nullable
as int?,appMasDepartmentDisplay: null == appMasDepartmentDisplay ? _self.appMasDepartmentDisplay : appMasDepartmentDisplay // ignore: cast_nullable_to_non_nullable
as String,website: freezed == website ? _self.website : website // ignore: cast_nullable_to_non_nullable
as String?,createDate: null == createDate ? _self.createDate : createDate // ignore: cast_nullable_to_non_nullable
as DateTime,isNotification: null == isNotification ? _self.isNotification : isNotification // ignore: cast_nullable_to_non_nullable
as String,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as String,statusDisplay: null == statusDisplay ? _self.statusDisplay : statusDisplay // ignore: cast_nullable_to_non_nullable
as String,lang: null == lang ? _self.lang : lang // ignore: cast_nullable_to_non_nullable
as String,userType: null == userType ? _self.userType : userType // ignore: cast_nullable_to_non_nullable
as UserType,
  ));
}


}

// dart format on
