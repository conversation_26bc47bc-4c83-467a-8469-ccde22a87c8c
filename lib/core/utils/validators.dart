import 'package:flutter_gen/gen_l10n/app_localizations.dart';

/// Utility class for various validation functions
class Validators {
  Validators._();

  /// Validates Thai ID card number (13 digits with checksum validation)
  /// Returns error message if invalid, null if valid
  static String? validateThaiIdCard(String? value, [AppLocalizations? l10n]) {
    if (value == null || value.isEmpty) {
      return null; // Let required validation handle empty values
    }

    // Remove any spaces or dashes
    final cleanValue = value.replaceAll(RegExp(r'[\s-]'), '');

    // Check if it contains exactly 13 digits
    final regex = RegExp(r'^[0-9]{13}$');
    if (!regex.hasMatch(cleanValue)) {
      return l10n?.validateIdCardLength ?? 'กรุณาระบุตัวเลข 13 หลัก';
    }

    // Calculate checksum using Thai ID card algorithm
    int sum = 0;
    for (int i = 0; i < 12; i++) {
      sum += int.parse(cleanValue[i]) * (13 - i);
    }

    final checkDigit = (11 - (sum % 11)) % 10;
    if (checkDigit != int.parse(cleanValue[12])) {
      return l10n?.validateIdCardFormat ?? 'กรุณาระบุให้ถูกต้อง';
    }

    return null; // Valid ID card
  }

  /// Validates username format and length
  /// Returns error message if invalid, null if valid
  static String? validateUsername(String? value, [AppLocalizations? l10n]) {
    if (value == null || value.isEmpty) {
      return null; // Let required validation handle empty values
    }

    // Check minimum length
    if (value.length < 4) {
      return l10n?.validateUsernameLength ?? 'กรุณาระบุอย่างน้อย 4 ตัวอักษร';
    }

    // Check character pattern: a-z, A-Z, 0-9, @, _, -, . only
    final usernameRegExp = RegExp(r'^[a-zA-Z0-9@_.-]+$');
    if (!usernameRegExp.hasMatch(value)) {
      return l10n?.validateUsernamePattern ??
          'ภาษาอังกฤษ ตัวเลข และสัญลักษณ์ @ _ - . เท่านั้น';
    }

    return null;
  }

  /// Validates email format
  static String? validateEmail(String? value, [AppLocalizations? l10n]) {
    if (value == null || value.isEmpty) {
      return null; // Let required validation handle empty values
    }

    final emailRegExp = RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$');
    if (!emailRegExp.hasMatch(value)) {
      return l10n?.validateEmailFormat ?? 'รูปแบบอีเมลไม่ถูกต้อง';
    }

    return null;
  }

  /// Validates phone number (exactly 10 digits)
  static String? validatePhone(String? value, [AppLocalizations? l10n]) {
    if (value == null || value.isEmpty) {
      return null; // Let required validation handle empty values
    }

    // Check if it contains only numbers
    final numberRegExp = RegExp(r'^[0-9]+$');
    if (!numberRegExp.hasMatch(value)) {
      return l10n?.validatePhoneNumber ?? 'ตัวเลขเท่านั้น';
    }

    // Check if it has exactly 10 digits
    if (value.length != 10) {
      return l10n?.validatePhoneLength ?? 'กรุณาระบุตัวเลข 10 หลัก';
    }

    return null;
  }

  /// Validates password (minimum 4 characters)
  static String? validatePassword(String? value, [AppLocalizations? l10n]) {
    if (value == null || value.isEmpty) {
      return null; // Let required validation handle empty values
    }

    if (value.length < 4) {
      return l10n?.validatePasswordLength ?? 'กรุณาระบุอย่างน้อย 4 ตัวอักษร';
    }

    return null;
  }

  /// Validates confirm password matches original password
  static String? validateConfirmPassword(
    String? value,
    String? originalPassword,
  ) {
    if (value == null || value.isEmpty) {
      return null; // Let required validation handle empty values
    }

    if (value != originalPassword) {
      return 'รหัสผ่านไม่ตรงกัน';
    }

    return null;
  }

  /// Validates required field
  static String? validateRequired(String? value, String fieldName) {
    if (value == null || value.isEmpty) {
      return 'กรุณากรอก$fieldName';
    }
    return null;
  }
}
