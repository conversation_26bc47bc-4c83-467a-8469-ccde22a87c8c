import 'package:mcdc/core/api/model/result.dart';

/// A generic API response parser that can handle different response formats
/// This replaces the need for a specific ApiResponse class and provides flexibility
class ApiResponseParser {
  /// Parse a standard API response with the format:
  /// {
  ///   "status": true,
  ///   "error_message": null,
  ///   "error_code": null,
  ///   "data": {},
  ///   "api_version": "v.0.0.1"
  /// }
  static Result<T> parseStandardResponse<T>(
    Map<String, dynamic> json,
    T Function(Map<String, dynamic>) fromJsonT,
  ) {
    try {
      final status = json['status'] as bool? ?? false;
      final errorMessage = json['error_message'] as String?;
      final errorCode = json['error_code'] as String?;
      final data = json['data'];
      
      if (status && data != null) {
        final parsedData = fromJsonT(data as Map<String, dynamic>);
        return Result.success(parsedData);
      } else {
        return Result.error(
          message: errorMessage ?? 'Unknown error occurred',
          code: errorCode,
        );
      }
    } catch (e) {
      return Result.error(
        message: 'Failed to parse response: ${e.toString()}',
        exception: e is Exception ? e : Exception(e.toString()),
      );
    }
  }

  /// Parse a direct data response (when the response is the data itself)
  /// Example: { "id": 1, "name": "John" }
  static Result<T> parseDirectResponse<T>(
    Map<String, dynamic> json,
    T Function(Map<String, dynamic>) fromJsonT,
  ) {
    try {
      final parsedData = fromJsonT(json);
      return Result.success(parsedData);
    } catch (e) {
      return Result.error(
        message: 'Failed to parse direct response: ${e.toString()}',
        exception: e is Exception ? e : Exception(e.toString()),
      );
    }
  }

  /// Parse a list response
  /// Example: [{ "id": 1, "name": "John" }, { "id": 2, "name": "Jane" }]
  static Result<List<T>> parseListResponse<T>(
    List<dynamic> json,
    T Function(Map<String, dynamic>) fromJsonT,
  ) {
    try {
      final parsedList = json
          .map((item) => fromJsonT(item as Map<String, dynamic>))
          .toList();
      return Result.success(parsedList);
    } catch (e) {
      return Result.error(
        message: 'Failed to parse list response: ${e.toString()}',
        exception: e is Exception ? e : Exception(e.toString()),
      );
    }
  }

  /// Parse a wrapped list response
  /// Example: { "data": [{ "id": 1, "name": "John" }], "total": 10 }
  static Result<List<T>> parseWrappedListResponse<T>(
    Map<String, dynamic> json,
    T Function(Map<String, dynamic>) fromJsonT, {
    String dataKey = 'data',
  }) {
    try {
      final data = json[dataKey] as List<dynamic>?;
      if (data != null) {
        final parsedList = data
            .map((item) => fromJsonT(item as Map<String, dynamic>))
            .toList();
        return Result.success(parsedList);
      } else {
        return Result.error(
          message: 'No data found in response',
        );
      }
    } catch (e) {
      return Result.error(
        message: 'Failed to parse wrapped list response: ${e.toString()}',
        exception: e is Exception ? e : Exception(e.toString()),
      );
    }
  }

  /// Parse a custom response format
  /// This allows for complete flexibility in parsing any response format
  static Result<T> parseCustomResponse<T>(
    dynamic json,
    Result<T> Function(dynamic) customParser,
  ) {
    try {
      return customParser(json);
    } catch (e) {
      return Result.error(
        message: 'Failed to parse custom response: ${e.toString()}',
        exception: e is Exception ? e : Exception(e.toString()),
      );
    }
  }

  /// Parse a response that might be in different formats
  /// This method tries different parsing strategies
  static Result<T> parseFlexibleResponse<T>(
    dynamic json,
    T Function(Map<String, dynamic>) fromJsonT,
  ) {
    try {
      // If it's a Map, try different parsing strategies
      if (json is Map<String, dynamic>) {
        // Try standard format first
        if (json.containsKey('status') && json.containsKey('data')) {
          return parseStandardResponse(json, fromJsonT);
        }
        // Try direct format
        else {
          return parseDirectResponse(json, fromJsonT);
        }
      }
      // If it's a List, parse as list
      else if (json is List) {
        return Result.error(
          message: 'List response not supported for single object parsing',
        );
      }
      // Unknown format
      else {
        return Result.error(
          message: 'Unknown response format',
        );
      }
    } catch (e) {
      return Result.error(
        message: 'Failed to parse flexible response: ${e.toString()}',
        exception: e is Exception ? e : Exception(e.toString()),
      );
    }
  }
}
